import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { Chat, ChatMessage, ChatSource } from '../pages/ai-chat/ai-chat.component';

export interface AiResponse {
  content: string;
  sources?: ChatSource[];
}

@Injectable({
  providedIn: 'root'
})
export class AiChatService {
  private chatsSubject = new BehaviorSubject<Chat[]>([]);

  constructor() {
    // Initialize with some demo chats
    this.initializeDemoChats();
  }

  // Get all chats
  getChats(): Observable<Chat[]> {
    return this.chatsSubject.asObservable();
  }

  // Create new chat
  createNewChat(): Chat {
    const newChat: Chat = {
      id: this.generateId(),
      title: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      isPinned: false,
      messages: []
    };

    const currentChats = this.chatsSubject.value;
    const updatedChats = [newChat, ...currentChats];
    this.chatsSubject.next(updatedChats);

    return newChat;
  }

  // Update chat
  updateChat(chatId: string, updates: Partial<Chat>): void {
    const currentChats = this.chatsSubject.value;
    const updatedChats = currentChats.map(chat =>
      chat.id === chatId
        ? { ...chat, ...updates, updatedAt: new Date() }
        : chat
    );

    this.chatsSubject.next(updatedChats);
  }

  // Delete chat
  deleteChat(chatId: string): void {
    const currentChats = this.chatsSubject.value;
    const updatedChats = currentChats.filter(chat => chat.id !== chatId);

    this.chatsSubject.next(updatedChats);
  }

  // Send message to AI (local simulation)
  sendMessage(chatId: string, message: string): Observable<AiResponse> {
    // Add user message immediately
    this.addMessageToChat(chatId, {
      id: this.generateId(),
      content: message,
      role: 'user',
      timestamp: new Date()
    });

    // Simulate AI response with delay
    return this.simulateAiResponse(message).pipe(
      delay(1500 + Math.random() * 2000) // 1.5-3.5 seconds delay
    );
  }

  // Add message to chat
  private addMessageToChat(chatId: string, message: ChatMessage): void {
    const currentChats = this.chatsSubject.value;
    const updatedChats = currentChats.map(chat => {
      if (chat.id === chatId) {
        const updatedMessages = [...chat.messages, message];
        
        // Auto-generate title from first user message if no title exists
        let title = chat.title;
        if (!title && message.role === 'user' && updatedMessages.filter(m => m.role === 'user').length === 1) {
          title = message.content.slice(0, 30);
          if (title.length < message.content.length) {
            title += '...';
          }
        }

        return {
          ...chat,
          title,
          messages: updatedMessages,
          updatedAt: new Date()
        };
      }
      return chat;
    });

    this.chatsSubject.next(updatedChats);
  }

  // Simulate AI response (replace with actual AI API call)
  private simulateAiResponse(message: string): Observable<AiResponse> {
    const responses = [
      {
        content: `Благодарю за ваш вопрос о "${message.slice(0, 20)}...". 

В традиции адвайта-веданты это понимается как путь к осознанию единства всего сущего. Согласно учению **Шри Рамана Махарши** [1], истинная природа нашего "Я" не отлична от Абсолютной Реальности.

Как говорится в **Упанишадах** [2]: "Тат твам аси" - "То есть ты". Это указывает на фундаментальное единство индивидуального сознания с универсальным Сознанием.

Для практического применения рекомендуется:
- Регулярная медитация самоисследования
- Изучение священных текстов
- Общение с реализованными учителями

*Помните: путь к истине лежит через прямое переживание, а не только интеллектуальное понимание.*`,
        sources: [
          {
            id: '1',
            title: 'Учение Шри Рамана Махарши о самоисследовании',
            content: 'Шри Рамана Махарши учил, что истинная природа нашего "Я" не отлична от Абсолютной Реальности. Он подчеркивал важность вопроса "Кто я?" как основного метода самоисследования. Через постоянное обращение внимания к источнику мысли "я", практикующий может прийти к прямому переживанию своей истинной природы, которая есть чистое Сознание, лишенное всех ограничений и двойственности.',
            highlightedText: 'истинная природа нашего "Я" не отлична от Абсолютной Реальности'
          },
          {
            id: '2',
            title: 'Чхандогья Упанишада - Тат твам аси',
            content: 'В Чхандогья Упанишаде содержится знаменитое изречение "Тат твам аси" (То есть ты), которое является одним из четырех великих изречений (махавакья) веданты. Это утверждение указывает на фундаментальное единство индивидуального сознания (дживы) с универсальным Сознанием (Брахманом). Учитель Удалака объясняет своему сыну Шветакету, что сущность всего проявленного мира и сущность индивидуального существа - одна и та же.',
            highlightedText: 'Тат твам аси'
          }
        ]
      },
      {
        content: `Отличный вопрос! В контексте духовной практики это имеет глубокое значение.

**Медитация** - это не просто техника расслабления, а путь к познанию истинной природы ума. В традиции **випассаны** [1] мы учимся наблюдать за потоком мыслей и ощущений без привязанности.

Основные принципы:

1. **Осознанность** (сати) - ясное видение происходящего в настоящий момент
2. **Концентрация** (самадхи) - однонаправленность ума  
3. **Мудрость** (панна) - понимание природы реальности

Как отмечает **Будда в Сатипаттхана сутте** [2]: "Есть только один путь для очищения существ, для преодоления печали и стенаний..."

Начните с простого наблюдения за дыханием по 10-15 минут ежедневно.`,
        sources: [
          {
            id: '1',
            title: 'Основы медитации випассана',
            content: 'Випассана, или медитация прозрения, является одной из древнейших техник медитации Индии. Она была заново открыта Буддой более 2500 лет назад и преподается как универсальное средство для духовного развития. Випассана означает видение вещей такими, какие они есть на самом деле. Это процесс самоочищения через самонаблюдение.',
            highlightedText: 'путь к познанию истинной природы ума'
          },
          {
            id: '2',
            title: 'Сатипаттхана сутта - Основы осознанности',
            content: 'Сатипаттхана сутта является одним из важнейших текстов буддийской традиции, посвященных практике осознанности. В этой сутте Будда подробно объясняет четыре основы осознанности: осознанность тела, чувств, ума и объектов ума. Он говорит: "Есть только один путь для очищения существ, для преодоления печали и стенаний, для исчезновения боли и горя, для достижения правильного пути, для реализации Ниббаны - это четыре основы осознанности."',
            highlightedText: 'Есть только один путь для очищения существ'
          }
        ]
      }
    ];

    // Randomly select a response
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // Simulate network delay
    return of(randomResponse).pipe(
      delay(2000 + Math.random() * 3000) // 2-5 seconds delay
    );
  }

  // Initialize with demo chats
  private initializeDemoChats(): void {
    const demoChats: Chat[] = [
      {
        id: 'demo-1',
        title: 'О медитации',
        createdAt: new Date(Date.now() - 86400000), // 1 day ago
        updatedAt: new Date(Date.now() - 86400000),
        isPinned: true,
        messages: [
          {
            id: 'msg-1',
            content: 'Расскажи о медитации для начинающих',
            role: 'user',
            timestamp: new Date(Date.now() - 86400000)
          },
          {
            id: 'msg-2',
            content: 'Медитация - это древняя практика, которая помогает успокоить ум и достичь внутреннего покоя. Для начинающих рекомендуется начать с простых техник наблюдения за дыханием.',
            role: 'assistant',
            timestamp: new Date(Date.now() - 86400000 + 60000)
          }
        ]
      }
    ];

    this.chatsSubject.next(demoChats);
  }

  // Generate unique ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

.carousel {
  position: relative;
  overflow: hidden;
  width: 100%;

  .carousel-track-wrapper {
    overflow: hidden;
  }

  .carousel-track {
    display: flex;
    transition: transform 0.4s ease-in-out;
    width: 100%;
  }

  ::ng-deep .carousel-item {
    flex: 0 0 calc(100% / var(--items-per-view, 3));
    box-sizing: border-box;
    padding: 0 0.5rem;
  }

  .nav {
    position: absolute;
    top: 40%;
    transform: translateY(-50%);
    background: #fff;
    border: none;
    cursor: pointer;
    font-size: 2rem;
    z-index: 2;

    &.left {
      left: 0.5rem;
    }
    &.right {
      right: 0.5rem;
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
  }

  .indicators {
    text-align: center;
    margin-top: 0.5rem;

    .dot {
      display: inline-block;
      width: 10px;
      height: 10px;
      background: #ccc;
      border-radius: 50%;
      margin: 0 4px;
      cursor: pointer;

      &.active {
        background: #333;
      }
    }
  }
}

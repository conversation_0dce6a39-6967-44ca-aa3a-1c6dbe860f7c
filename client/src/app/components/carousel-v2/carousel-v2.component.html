<div class="carousel">
  <!-- Лента -->
  <div class="carousel-track-wrapper">
    <div
      class="carousel-track"
      [style.transform]="'translateX(-' + currentIndex() * (100 / itemsPerView) + '%)'"
    >
      <ng-container *ngFor="let item of items">
        <ng-container
          *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"
        ></ng-container>
      </ng-container>
    </div>
  </div>

  <!-- Кнопки -->
  <button class="nav left" (click)="prev()" [disabled]="currentIndex() === 0">
    ‹
  </button>
  <button
    class="nav right"
    (click)="next()"
    [disabled]="currentIndex() === totalPages() - 1"
  >
    ›
  </button>

  <!-- Индикаторы -->
  <div class="indicators">
    <span
      *ngFor="let i of [].constructor(totalPages()); let idx = index"
      class="dot"
      [class.active]="idx === currentIndex()"
      (click)="goTo(idx)"
    ></span>
  </div>
</div>

import {
  Component,
  Input,
  ContentChild,
  TemplateRef,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-carousel-v2',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './carousel-v2.component.html',
  styleUrl: './carousel-v2.component.scss'
})
export class CarouselV2<T> {
  @Input({ required: true }) items: T[] = [];
  @Input() itemsPerView = 3;

  @ContentChild(TemplateRef) itemTemplate!: TemplateRef<any>;

  currentIndex = signal(0);

  totalPages = computed(() =>
    Math.ceil(this.items.length / this.itemsPerView)
  );

  next() {
    if (this.currentIndex() < this.totalPages() - 1) {
      this.currentIndex.set(this.currentIndex() + 1);
    }
  }

  prev() {
    if (this.currentIndex() > 0) {
      this.currentIndex.set(this.currentIndex() - 1);
    }
  }

  goTo(index: number) {
    this.currentIndex.set(index);
  }
}

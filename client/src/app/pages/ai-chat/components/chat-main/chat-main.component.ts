import { Component, input, output, signal, computed, ViewChild, ElementRef, AfterViewChecked, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Chat, ChatMessage, ChatSource } from '../../ai-chat.component';
import { ChatMessageComponent } from '../chat-message/chat-message.component';

@Component({
  selector: 'app-chat-main',
  standalone: true,
  imports: [CommonModule, FormsModule, ChatMessageComponent],
  templateUrl: './chat-main.component.html',
  styleUrl: './chat-main.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatMainComponent implements AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Inputs
  chat = input<Chat | null>(null);
  isLoading = input<boolean>(false);

  // Outputs
  sendMessage = output<string>();
  sourceClick = output<ChatSource>();

  // State
  messageText = signal<string>('');
  isFirstChat = signal<boolean>(true);

  // Computed
  hasMessages = computed(() => {
    const currentChat = this.chat();
    return currentChat && currentChat.messages.length > 0;
  });

  chatTitle = computed(() => {
    const currentChat = this.chat();
    if (!currentChat) return '';
    
    if (currentChat.title && currentChat.title.trim()) {
      return currentChat.title;
    }
    
    // Generate title from first user message
    const firstUserMessage = currentChat.messages.find(m => m.role === 'user');
    if (firstUserMessage) {
      const title = firstUserMessage.content.slice(0, 50);
      return title.length < firstUserMessage.content.length ? title + '...' : title;
    }
    
    return 'Новый чат';
  });

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  onSendMessage() {
    const text = this.messageText().trim();
    if (!text || this.isLoading()) return;

    this.sendMessage.emit(text);
    this.messageText.set('');
    this.isFirstChat.set(false);
    
    // Reset textarea height
    if (this.messageInput?.nativeElement) {
      this.messageInput.nativeElement.style.height = 'auto';
    }
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.onSendMessage();
    }
  }

  onInput(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    
    // Auto-resize textarea
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  }

  onSourceClick(source: ChatSource) {
    this.sourceClick.emit(source);
  }

  private scrollToBottom() {
    if (this.messagesContainer?.nativeElement) {
      const container = this.messagesContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

  getLoadingText(): string {
    const loadingStates = [
      'Обрабатываю ваш запрос...',
      'Анализирую источники...',
      'Формирую ответ...'
    ];
    
    // Cycle through loading states every 2 seconds
    const index = Math.floor(Date.now() / 2000) % loadingStates.length;
    return loadingStates[index];
  }
}

@use "../../../../styles/core.scss" as core;

.chat-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--grey-back);
}

// Chat Header
.chat-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border);
  background: var(--dr-back);
}

.chat-title {
  font-family: Prata;
  font-size: 20px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Messages Container
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--light-color);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--book_about);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
  }
}

.messages-list {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// Welcome State
.welcome-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 24px;
}

.welcome-content {
  text-align: center;
  max-width: 500px;
}

.welcome-icon {
  font-size: 64px;
  margin-bottom: 24px;
}

.welcome-title {
  font-family: Prata;
  font-size: 28px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 16px 0;
}

.welcome-description {
  font-family: IBM_Plex_Sans;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  margin: 0 0 32px 0;
}

.welcome-suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.suggestion-btn {
  padding: 12px 20px;
  background: var(--button_);
  border: 1px solid core.$light2;
  border-radius: 8px;
  color: var(--font-color1);
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 280px;

  &:hover {
    background: var(--button_figure);
    color: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
  }
}

// No Chat State
.no-chat-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 24px;
}

.no-chat-content {
  text-align: center;
  max-width: 400px;
}

.no-chat-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.no-chat-title {
  font-family: Prata;
  font-size: 24px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 16px 0;
}

.no-chat-description {
  font-family: IBM_Plex_Sans;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  opacity: 0.7;
  margin: 0;
}

// Input Area
.input-area {
  padding: 20px 24px;
  background: var(--dr-back);
  border-top: 1px solid var(--border);
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: var(--grey-back);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 12px 16px;
  transition: border-color 0.2s ease;

  &:focus-within {
    border-color: core.$light2;
  }
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-family: IBM_Plex_Sans;
  font-size: 16px;
  line-height: 1.4;
  color: var(--font-color1);
  resize: none;
  min-height: 24px;
  max-height: 120px;

  &::placeholder {
    color: var(--text-color);
    opacity: 0.6;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: core.$light2;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: core.$light4;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .send-icon {
    font-size: 18px;
    color: white;
  }
}

// Loading Spinner
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Loading Indicator
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 12px;
  padding: 8px 16px;
  background: var(--selection);
  border-radius: 8px;
}

.loading-text {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  color: var(--font-color1);
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background: var(--font-color1);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;

  &:nth-child(1) { animation-delay: -0.32s; }
  &:nth-child(2) { animation-delay: -0.16s; }
  &:nth-child(3) { animation-delay: 0s; }
}

@keyframes pulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

// Mobile Styles
@media (max-width: 768px) {
  .chat-header {
    padding: 16px 20px;
  }

  .chat-title {
    font-size: 18px;
  }

  .messages-list {
    padding: 16px 20px;
  }

  .welcome-state,
  .no-chat-state {
    padding: 30px 20px;
  }

  .welcome-icon,
  .no-chat-icon {
    font-size: 48px;
  }

  .welcome-title,
  .no-chat-title {
    font-size: 22px;
  }

  .welcome-description,
  .no-chat-description {
    font-size: 14px;
  }

  .input-area {
    padding: 16px 20px;
  }

  .input-wrapper {
    padding: 10px 12px;
  }

  .message-input {
    font-size: 14px;
  }

  .send-button {
    width: 36px;
    height: 36px;
  }

  .suggestion-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

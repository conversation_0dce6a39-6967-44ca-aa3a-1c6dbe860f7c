@use "../../../../styles/core.scss" as core;

.chat-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--grey-back);
  position: relative;
}

// Chat Header
.chat-header {
  padding: 24px 32px;
  border-bottom: 2px solid var(--border);
  background: linear-gradient(135deg, var(--side_back) 0%, var(--light-color) 100%);
  box-shadow: 0 2px 8px rgba(83, 46, 0, 0.1);
}

.chat-title {
  font-family: Prata, serif;
  font-size: 22px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(83, 46, 0, 0.1);
}

// Messages Container
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background: var(--grey-back);

  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-color);
    border-radius: 5px;
    border: 2px solid var(--grey-back);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: core.$light2;
  }
}

.messages-list {
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 100%;
}

// Welcome State
.welcome-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 32px;
  background: radial-gradient(ellipse at center, var(--light-color) 0%, var(--grey-back) 70%);
}

.welcome-content {
  text-align: center;
  max-width: 600px;
  background: rgba(255, 255, 255, 0.6);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(83, 46, 0, 0.15);
  border: 2px solid var(--border);
}

.welcome-icon {
  font-size: 72px;
  margin-bottom: 24px;
  filter: drop-shadow(0 4px 8px rgba(83, 46, 0, 0.2));
}

.welcome-title {
  font-family: Prata, serif;
  font-size: 32px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 20px 0;
  text-shadow: 0 2px 4px rgba(83, 46, 0, 0.1);
}

.welcome-description {
  font-family: IBM_Plex_Sans, sans-serif;
  font-size: 18px;
  line-height: 1.6;
  color: var(--font-color1);
  margin: 0 0 36px 0;
  opacity: 0.8;
}

.welcome-suggestions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.suggestion-btn {
  padding: 16px 24px;
  background: var(--button_);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid core.$light2;
  border-radius: 12px;
  color: var(--font-color1);
  font-family: IBM_Plex_Sans, sans-serif;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: 320px;
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

  &:hover {
    background: var(--button_figure);
    background-size: cover;
    color: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(83, 46, 0, 0.25);
  }
}

// No Chat State
.no-chat-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 24px;
}

.no-chat-content {
  text-align: center;
  max-width: 400px;
}

.no-chat-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.no-chat-title {
  font-family: Prata;
  font-size: 24px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 16px 0;
}

.no-chat-description {
  font-family: IBM_Plex_Sans;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  opacity: 0.7;
  margin: 0;
}

// Input Area
.input-area {
  padding: 24px 32px;
  background: linear-gradient(135deg, var(--side_back) 0%, var(--light-color) 100%);
  border-top: 2px solid var(--border);
  box-shadow: 0 -2px 8px rgba(83, 46, 0, 0.1);
}

.input-container {
  max-width: 900px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid var(--border);
  border-radius: 16px;
  padding: 16px 20px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(83, 46, 0, 0.1);

  &:focus-within {
    border-color: core.$light2;
    box-shadow: 0 6px 24px rgba(222, 165, 61, 0.2);
    transform: translateY(-2px);
  }
}

.message-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-family: IBM_Plex_Sans, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--font-color1);
  resize: none;
  min-height: 28px;
  max-height: 140px;

  &::placeholder {
    color: var(--text-color);
    opacity: 0.7;
    font-style: italic;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--button_);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 2px solid core.$light2;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(83, 46, 0, 0.15);

  &:hover:not(:disabled) {
    background: var(--button_figure);
    background-size: cover;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(83, 46, 0, 0.25);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .send-icon {
    font-size: 20px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

// Loading Spinner
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Loading Indicator
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 12px;
  padding: 8px 16px;
  background: var(--selection);
  border-radius: 8px;
}

.loading-text {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  color: var(--font-color1);
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background: var(--font-color1);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite both;

  &:nth-child(1) { animation-delay: -0.32s; }
  &:nth-child(2) { animation-delay: -0.16s; }
  &:nth-child(3) { animation-delay: 0s; }
}

@keyframes pulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

// Mobile Styles
@media (max-width: 768px) {
  .chat-header {
    padding: 16px 20px;
  }

  .chat-title {
    font-size: 18px;
  }

  .messages-list {
    padding: 16px 20px;
  }

  .welcome-state,
  .no-chat-state {
    padding: 30px 20px;
  }

  .welcome-icon,
  .no-chat-icon {
    font-size: 48px;
  }

  .welcome-title,
  .no-chat-title {
    font-size: 22px;
  }

  .welcome-description,
  .no-chat-description {
    font-size: 14px;
  }

  .input-area {
    padding: 16px 20px;
  }

  .input-wrapper {
    padding: 10px 12px;
  }

  .message-input {
    font-size: 14px;
  }

  .send-button {
    width: 36px;
    height: 36px;
  }

  .suggestion-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

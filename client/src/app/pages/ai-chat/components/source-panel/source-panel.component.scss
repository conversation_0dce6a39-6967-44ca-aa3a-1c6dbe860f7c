@use "../../../../styles/core.scss" as core;

.source-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--dr-back);
  color: var(--font-color1);
  position: relative;
  overflow: hidden;
}

// Panel Header
.panel-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border);
  background: var(--dr-back);
  gap: 16px;
}

.header-content {
  flex: 1;
  min-width: 0;
}

.source-title {
  font-family: Prata;
  font-size: 18px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 8px 0;
  line-height: 1.3;
  word-wrap: break-word;
}

.source-url {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: var(--selection);
  border: 1px solid var(--border);
  border-radius: 6px;
  color: rgba(42, 124, 187, 1);
  text-decoration: none;
  font-family: IBM_Plex_Sans;
  font-size: 13px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--grey-back);
    color: rgba(105, 77, 164, 1);
    border-color: core.$light2;
  }
}

.url-icon {
  font-size: 12px;
}

.url-text {
  white-space: nowrap;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;

  &:hover {
    background: var(--selection);
    border-color: core.$light2;
  }
}

.close-icon {
  font-size: 16px;
  color: var(--font-color1);
  opacity: 0.7;
}

// Panel Content
.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--light-color);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--book_about);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
  }
}

.source-content {
  padding: 24px;
}

.content-text {
  font-family: IBM_Plex_Sans;
  font-size: 15px;
  line-height: 1.6;
  color: var(--font-color1);

  p {
    margin: 0 0 16px 0;
    text-align: justify;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Highlighted text styling
  :global(.highlight) {
    background: rgba(222, 165, 61, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
    color: var(--font-color1);
    border: 1px solid rgba(222, 165, 61, 0.5);
    animation: highlight-pulse 2s ease-in-out;
  }

  // Text formatting
  strong {
    font-weight: 600;
    color: var(--font-color1);
  }

  em {
    font-style: italic;
  }

  // Links in content
  a {
    color: rgba(42, 124, 187, 1);
    text-decoration: underline;

    &:hover {
      color: rgba(105, 77, 164, 1);
    }

    &:visited {
      color: rgba(105, 77, 164, 1);
    }
  }
}

@keyframes highlight-pulse {
  0% {
    background: rgba(222, 165, 61, 0.5);
    transform: scale(1);
  }
  50% {
    background: rgba(222, 165, 61, 0.7);
    transform: scale(1.02);
  }
  100% {
    background: rgba(222, 165, 61, 0.3);
    transform: scale(1);
  }
}

// Panel Footer
.panel-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border);
  background: var(--selection);
}

.footer-info {
  text-align: center;
}

.info-text {
  font-family: IBM_Plex_Sans;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
}

// Empty Panel
.empty-panel {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 24px;
}

.empty-content {
  text-align: center;
  max-width: 280px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-family: Prata;
  font-size: 18px;
  font-weight: 400;
  color: var(--font-color1);
  margin: 0 0 12px 0;
}

.empty-description {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color);
  opacity: 0.7;
}

// Mobile Backdrop
.mobile-backdrop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

// Mobile Styles
@media (max-width: 1024px) {
  .source-panel {
    position: fixed;
    top: 80px; // Header height
    right: 0;
    bottom: 0;
    z-index: 105;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);

    &.panel-open {
      .mobile-backdrop {
        display: block;
      }
    }
  }

  .mobile-backdrop {
    z-index: -1;
  }
}

@media (max-width: 768px) {
  .panel-header {
    padding: 16px 20px;
  }

  .source-title {
    font-size: 16px;
  }

  .source-url {
    padding: 5px 10px;
    font-size: 12px;
  }

  .close-btn {
    width: 28px;
    height: 28px;
  }

  .close-icon {
    font-size: 14px;
  }

  .source-content {
    padding: 20px;
  }

  .content-text {
    font-size: 14px;
    line-height: 1.5;
  }

  .panel-footer {
    padding: 12px 20px;
  }

  .info-text {
    font-size: 11px;
  }

  .empty-panel {
    padding: 30px 20px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
  }
}

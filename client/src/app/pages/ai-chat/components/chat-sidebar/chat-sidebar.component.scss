@use "../../../../styles/core.scss" as core;

.chat-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--dr-back);
  color: var(--font-color1);
}

// Header
.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid var(--border);
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--button_);
  border: 1px solid core.$light2;
  border-radius: 10px;
  color: var(--font-color1);
  font-family: Prata;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--button_figure);
    color: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
  }

  .new-chat-icon {
    font-size: 18px;
    font-weight: bold;
  }
}

// Chat List Container
.chat-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--book_about);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
  }
}

// Chat List
.chat-list {
  padding: 0 8px;
}

// Chat Item
.chat-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &:hover {
    background: var(--selection);
    
    .chat-actions {
      opacity: 1;
      visibility: visible;
    }
  }

  &.active {
    background: var(--selection);
    border-color: core.$light2;
  }

  &.pinned {
    border-left: 3px solid core.$light2;
  }
}

// Pin Indicator
.pin-indicator {
  margin-right: 8px;
  
  .pin-icon {
    font-size: 12px;
    opacity: 0.7;
  }
}

// Chat Content
.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-title {
  font-family: Prata;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.3;
  color: var(--font-color1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-date {
  font-family: IBM_Plex_Sans;
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
}

.chat-title-input {
  width: 100%;
  background: var(--grey-back);
  border: 1px solid core.$light2;
  border-radius: 4px;
  padding: 4px 8px;
  font-family: Prata;
  font-size: 14px;
  color: var(--font-color1);
  outline: none;

  &:focus {
    border-color: core.$light4;
  }
}

// Chat Actions
.chat-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--grey-back);
  }

  .action-icon {
    font-size: 12px;
    opacity: 0.7;
  }
}

.pin-btn .pin-icon {
  opacity: 0.5;
}

.unpin-btn .unpin-icon {
  opacity: 1;
  color: core.$light2;
}

.delete-btn:hover {
  background: rgba(194, 30, 17, 0.1);
  
  .delete-icon {
    opacity: 1;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  height: 100%;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-family: Prata;
  font-size: 18px;
  font-weight: 400;
  color: var(--font-color1);
  margin-bottom: 8px;
}

.empty-description {
  font-family: IBM_Plex_Sans;
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.7;
  line-height: 1.4;
  max-width: 200px;
}

// Mobile Styles
@media (max-width: 768px) {
  .sidebar-header {
    padding: 16px 12px;
  }

  .new-chat-btn {
    padding: 10px 12px;
    font-size: 14px;
  }

  .chat-item {
    padding: 10px 12px;
  }

  .chat-title {
    font-size: 13px;
  }

  .chat-date {
    font-size: 11px;
  }

  .empty-state {
    padding: 30px 16px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-title {
    font-size: 16px;
  }

  .empty-description {
    font-size: 13px;
  }
}

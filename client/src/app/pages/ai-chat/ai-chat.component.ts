import { Component, inject, signal, computed, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslocoService } from '@jsverse/transloco';
import { ChatSidebarComponent } from './components/chat-sidebar/chat-sidebar.component';
import { ChatMainComponent } from './components/chat-main/chat-main.component';
import { SourcePanelComponent } from './components/source-panel/source-panel.component';
import { AiChatService } from '../../services/ai-chat.service';

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  sources?: ChatSource[];
  isTyping?: boolean;
}

export interface ChatSource {
  id: string;
  title: string;
  content: string;
  url?: string;
  highlightedText?: string;
}

export interface Chat {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  isPinned: boolean;
  messages: ChatMessage[];
}

@Component({
  selector: 'app-ai-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChatSidebarComponent,
    ChatMainComponent,
    SourcePanelComponent
  ],
  templateUrl: './ai-chat.component.html',
  styleUrl: './ai-chat.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AiChatComponent implements OnInit {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private translocoService = inject(TranslocoService);
  private aiChatService = inject(AiChatService);

  // State signals
  chats = signal<Chat[]>([]);
  currentChatId = signal<string | null>(null);
  selectedSource = signal<ChatSource | null>(null);
  isSourcePanelOpen = signal<boolean>(false);
  isSidebarOpen = signal<boolean>(true);
  isLoading = signal<boolean>(false);

  // Computed values
  currentChat = computed(() => {
    const chatId = this.currentChatId();
    return this.chats().find(chat => chat.id === chatId) || null;
  });

  ngOnInit() {
    this.loadChats();
    
    // Check if there's a chat ID in the route
    this.route.params.subscribe(params => {
      if (params['chatId']) {
        this.currentChatId.set(params['chatId']);
      }
    });
  }

  private loadChats() {
    this.aiChatService.getChats().subscribe(chats => {
      this.chats.set(chats);
    });
  }

  onNewChat() {
    const newChat = this.aiChatService.createNewChat();
    this.chats.update(chats => [newChat, ...chats]);
    this.currentChatId.set(newChat.id);
    this.navigateToChat(newChat.id);
  }

  onChatSelect(chatId: string) {
    this.currentChatId.set(chatId);
    this.navigateToChat(chatId);
  }

  onChatRename(chatId: string, newTitle: string) {
    this.chats.update(chats => 
      chats.map(chat => 
        chat.id === chatId ? { ...chat, title: newTitle } : chat
      )
    );
    this.aiChatService.updateChat(chatId, { title: newTitle });
  }

  onChatDelete(chatId: string) {
    this.chats.update(chats => chats.filter(chat => chat.id !== chatId));
    this.aiChatService.deleteChat(chatId);
    
    if (this.currentChatId() === chatId) {
      const remainingChats = this.chats();
      if (remainingChats.length > 0) {
        this.onChatSelect(remainingChats[0].id);
      } else {
        this.currentChatId.set(null);
        this.router.navigate([`/${this.translocoService.getActiveLang()}/ai-chat`]);
      }
    }
  }

  onChatPin(chatId: string) {
    this.chats.update(chats => 
      chats.map(chat => 
        chat.id === chatId ? { ...chat, isPinned: !chat.isPinned } : chat
      )
    );
    this.aiChatService.updateChat(chatId, { isPinned: !this.chats().find(c => c.id === chatId)?.isPinned });
  }

  onSendMessage(content: string) {
    const currentChat = this.currentChat();
    if (!currentChat) return;

    const userMessage: ChatMessage = {
      id: this.generateId(),
      content,
      role: 'user',
      timestamp: new Date()
    };

    // Add user message
    this.updateChatMessages(currentChat.id, [...currentChat.messages, userMessage]);

    // Add typing indicator for AI
    const typingMessage: ChatMessage = {
      id: this.generateId(),
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isTyping: true
    };

    this.updateChatMessages(currentChat.id, [...currentChat.messages, userMessage, typingMessage]);
    this.isLoading.set(true);

    // Send to AI service
    this.aiChatService.sendMessage(currentChat.id, content).subscribe({
      next: (response) => {
        const aiMessage: ChatMessage = {
          id: this.generateId(),
          content: response.content,
          role: 'assistant',
          timestamp: new Date(),
          sources: response.sources
        };

        // Replace typing message with actual response
        const updatedMessages = currentChat.messages.slice(0, -1).concat(aiMessage);
        this.updateChatMessages(currentChat.id, updatedMessages);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error sending message:', error);
        // Remove typing message on error
        const updatedMessages = currentChat.messages.slice(0, -1);
        this.updateChatMessages(currentChat.id, updatedMessages);
        this.isLoading.set(false);
      }
    });
  }

  onSourceClick(source: ChatSource) {
    this.selectedSource.set(source);
    this.isSourcePanelOpen.set(true);
  }

  onSourcePanelClose() {
    this.isSourcePanelOpen.set(false);
    this.selectedSource.set(null);
  }

  onToggleSidebar() {
    this.isSidebarOpen.update(open => !open);
  }

  private updateChatMessages(chatId: string, messages: ChatMessage[]) {
    this.chats.update(chats => 
      chats.map(chat => 
        chat.id === chatId ? { ...chat, messages, updatedAt: new Date() } : chat
      )
    );
  }

  private navigateToChat(chatId: string) {
    this.router.navigate([`/${this.translocoService.getActiveLang()}/ai-chat/${chatId}`]);
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

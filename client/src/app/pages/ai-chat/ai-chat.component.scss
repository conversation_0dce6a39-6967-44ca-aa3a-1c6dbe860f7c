@use "../../styles/core.scss" as core;

.ai-chat-container {
  display: flex;
  height: 100vh;
  background: var(--grey-back);
  position: relative;
  overflow: hidden;
}

// Left Sidebar
app-chat-sidebar {
  width: 320px;
  min-width: 320px;
  background: var(--dr-back);
  border-right: 1px solid var(--border);
  transition: transform 0.3s ease;
  z-index: 100;

  &.sidebar-hidden {
    transform: translateX(-100%);
  }
}

// Main Chat Area
.chat-main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 0; // Prevents flex item from overflowing
}

.sidebar-toggle {
  display: none;
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 110;
  background: var(--dr-back);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--selection);
  }

  .burger-line {
    display: block;
    width: 20px;
    height: 2px;
    background: var(--font-color1);
    margin: 3px 0;
    transition: 0.3s;
  }

  &.sidebar-open {
    .burger-line:nth-child(1) {
      transform: rotate(-45deg) translate(-5px, 6px);
    }
    .burger-line:nth-child(2) {
      opacity: 0;
    }
    .burger-line:nth-child(3) {
      transform: rotate(45deg) translate(-5px, -6px);
    }
  }
}

// Right Panel
app-source-panel {
  width: 0;
  min-width: 0;
  background: var(--dr-back);
  border-left: 1px solid var(--border);
  transition: all 0.3s ease;
  overflow: hidden;

  &.panel-open {
    width: 400px;
    min-width: 400px;
  }
}

// Mobile Overlay
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 90;
}

// Mobile Styles
@media (max-width: 1024px) {
  .ai-chat-container {
    height: calc(100vh - 80px); // Account for header
  }

  app-chat-sidebar {
    position: fixed;
    top: 80px; // Header height
    left: 0;
    bottom: 0;
    z-index: 100;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

    &.sidebar-hidden {
      transform: translateX(-100%);
    }
  }

  .sidebar-toggle {
    display: block;

    &.mobile-only {
      display: block;
    }
  }

  .mobile-overlay {
    display: block;
  }

  app-source-panel {
    position: fixed;
    top: 80px;
    right: 0;
    bottom: 0;
    z-index: 105;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);

    &.panel-open {
      width: 100%;
      min-width: 100%;
    }
  }
}

@media (max-width: 768px) {
  app-chat-sidebar {
    width: 280px;
    min-width: 280px;
  }

  app-source-panel.panel-open {
    width: 100%;
    min-width: 100%;
  }
}

@media (max-width: 480px) {
  app-chat-sidebar {
    width: 100%;
    min-width: 100%;
  }

  .sidebar-toggle {
    top: 15px;
    left: 15px;
  }
}

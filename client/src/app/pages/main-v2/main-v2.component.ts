import { CarouselV2 } from '@/components/carousel-v2/carousel-v2.component';
import { Component } from '@angular/core';

@Component({
  selector: 'app-main-v2',
  imports: [CarouselV2],
  templateUrl: './main-v2.component.html',
  standalone: true,
  styleUrl: './main-v2.component.scss'
})
export class MainV2 {
  events = [
    {
    date: '15–20 мая 2025',
    title: 'Ретрит по медитации',
    description:
      'Погрузитесь в глубокую практику медитации под руководством опытных наставников в атмосфере духовного единства.',
    image: '/assets/images/events/meditation-retreat.jpg'
  },
  {
    date: '20 мая 2025',
    title: 'Лекция о Веданте',
    description:
      'Глубокое погружение в философию Адвайта Веданты с разбором ключевых концепций и практическим применением.',
    image: '/assets/images/events/vedanta-lecture.jpg'
  },
  {
    date: '25 мая 2025',
    title: 'Празднование Будда Пурнимы',
    description:
      'Присоединяйтесь к торжественному празднованию дня рождения, просветления и паринирваны Будды Шакьямуни.',
    image: '/assets/images/events/buddha-purnima.jpg'
  },
  {
    date: '10 июня 2025',
    title: 'Йога-день на природе',
    description:
      'Практика асан, дыхательных техник и медитации на свежем воздухе в гармонии с природой.',
    image: '/assets/images/events/yoga-day.jpg'
  }
];

courses = [
  {
    date: '1 сентября 2025',
    level: 'Начальный уровень',
    title: 'Основы медитации',
    description:
      'Базовый курс для начинающих, включающий теорию и практику различных техник медитации.',
    image: '/assets/images/courses/meditation-basics.jpg'
  },
  {
    date: '24 сентября 2025',
    level: 'Средний уровень',
    title: 'Углублённая практика медитации',
    description:
      'Расширение базовых знаний и освоение продвинутых техник концентрации и внимательности.',
    image: '/assets/images/courses/advanced-meditation.jpg'
  },
  {
    date: '10 октября 2025',
    level: 'Начальный уровень',
    title: 'Введение в йогу',
    description:
      'Курс, сочетающий асаны, пранаямы и основы философии йоги для формирования целостной практики.',
    image: '/assets/images/courses/yoga-intro.jpg'
  },
  {
    date: '5 ноября 2025',
    level: 'Продвинутый уровень',
    title: 'Философия Веданты',
    description:
      'Изучение ключевых понятий ведической философии и их практическое применение в жизни.',
    image: '/assets/images/courses/vedanta-philosophy.jpg'
  }
];
}
